# Ozon API 代码风格指南

## ✅ **已统一的风格规范**

### 1. **包结构**
```go
package ozonapi
```

### 2. **导入规范**
```go
import (
    "context"
    "fmt"
)
```

### 3. **服务结构体定义**
```go
// XxxService 服务描述，参考ozon-client的Xxx结构
type XxxService struct {
    *BaseService
}
```

### 4. **构造函数命名**
```go
// NewXxxService 创建Xxx服务
func NewXxxService(baseService *BaseService) *XxxService {
    return &XxxService{
        BaseService: baseService,
    }
}
```

### 5. **方法签名规范**
```go
// MethodName 方法描述
func (s *XxxService) MethodName(ctx context.Context, params *MethodParams) (*MethodResponse, error) {
    result, err := s.Request("POST", "/v1/endpoint", params)
    if err != nil {
        return nil, err
    }

    response := &MethodResponse{}
    if err := s.mapToStruct(result, response); err != nil {
        return nil, fmt.Errorf("failed to map response: %w", err)
    }

    return response, nil
}
```

### 6. **参数结构体命名**
- 请求参数: `XxxParams`
- 响应结构: `XxxResponse`
- 结果数据: `XxxResult`
- 过滤器: `XxxFilter`
- 配置项: `XxxWith`

### 7. **JSON标签规范**
```go
type ExampleStruct struct {
    // 字段描述
    FieldName string `json:"field_name"`
    // 可选字段
    OptionalField string `json:"optional_field,omitempty"`
}
```

### 8. **错误处理规范**
```go
if err != nil {
    return nil, fmt.Errorf("failed to do something: %w", err)
}
```

### 9. **注释规范**
```go
// PublicMethod 公共方法描述
//
// 详细说明方法的功能和用途
//
// 参数:
//   - param1: 参数1描述
//   - param2: 参数2描述
//
// 返回值描述
func (s *Service) PublicMethod(param1 string, param2 int) error {
    // 实现
}
```

### 10. **常量定义规范**
```go
const (
    // 枚举值描述
    EnumValue1 EnumType = "value1"
    EnumValue2 EnumType = "value2"
)
```

## 🔧 **已修正的不一致问题**

### 1. **构造函数命名统一**
- ❌ 修正前: `newFBOService` (小写开头)
- ✅ 修正后: `NewFBOService` (大写开头，与其他服务一致)

### 2. **移除重复的mapToStruct方法**
- ❌ 修正前: 每个服务都定义自己的mapToStruct方法
- ✅ 修正后: 统一使用BaseService中的mapToStruct方法

### 3. **移除重复的TimeFormat定义**
- ❌ 修正前: analytics_service.go中重复定义TimeFormat
- ✅ 修正后: 统一使用common.go中的TimeFormat定义

## 📋 **代码检查清单**

### 新增服务时需要检查：

- [ ] 服务结构体继承`*BaseService`
- [ ] 构造函数使用`NewXxxService`命名
- [ ] 方法签名包含`context.Context`参数
- [ ] 使用`s.Request()`而不是直接使用HTTP客户端
- [ ] 使用`s.mapToStruct()`进行响应映射
- [ ] 错误处理使用`fmt.Errorf`包装
- [ ] 参数和响应结构体命名规范
- [ ] JSON标签使用snake_case
- [ ] 添加适当的注释文档

### 类型定义检查：

- [ ] 枚举类型使用string类型
- [ ] 常量使用const块定义
- [ ] 结构体字段有适当的注释
- [ ] 可选字段使用omitempty标签
- [ ] 时间字段使用TimeFormat或DateFormat类型

### 文档检查：

- [ ] 公共方法有详细注释
- [ ] 参数和返回值有说明
- [ ] 复杂逻辑有内联注释
- [ ] 示例代码在README中更新

## 🎯 **最佳实践**

### 1. **错误处理**
```go
// 好的做法
if err != nil {
    return nil, fmt.Errorf("failed to get product list: %w", err)
}

// 避免的做法
if err != nil {
    return nil, err // 缺少上下文信息
}
```

### 2. **结构体初始化**
```go
// 好的做法
response := &GetProductListResponse{}

// 避免的做法
var response GetProductListResponse
```

### 3. **方法链式调用**
```go
// 保持一致的缩进
result, err := s.Request("POST", "/v1/endpoint", params)
if err != nil {
    return nil, err
}
```

### 4. **类型转换**
```go
// 使用统一的mapToStruct方法
if err := s.mapToStruct(result, response); err != nil {
    return nil, fmt.Errorf("failed to map response: %w", err)
}
```

## 🔍 **代码审查要点**

1. **一致性**: 确保新代码与现有代码风格一致
2. **可读性**: 变量和方法命名清晰明确
3. **错误处理**: 所有错误都有适当的处理和包装
4. **文档**: 公共API有完整的文档注释
5. **测试**: 新功能有对应的测试用例

## 📝 **版本历史**

- v1.0.0: 初始版本，建立基本风格规范
- v1.1.0: 修正构造函数命名不一致问题
- v1.1.1: 移除重复的mapToStruct和TimeFormat定义
- v1.1.2: 统一错误处理和响应映射方式
