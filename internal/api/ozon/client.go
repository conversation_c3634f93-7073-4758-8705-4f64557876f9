package ozonapi

import (
	"time"

	"github.com/go-resty/resty/v2"
)

const (
	defaultTimeout = 10 * time.Second
	defaultHost    = "https://api-seller.ozon.ru"
)

// Client Ozon API 客户端，参考ozon-client结构
type Client struct {
	httpClient *resty.Client // HTTP 请求客户端
	config     *Config       // 客户端配置

	// 主要服务模块 (参考ozon-client结构)
	Analytics     *AnalyticsService
	FBO           *FBOService
	FBS           *FBSService
	Finance       *FinanceService
	Products      *ProductsService
	Promotions    *PromotionsService
	Rating        *RatingService
	Warehouses    *WarehousesService
	Returns       *ReturnsService
	Reports       *ReportsService
	Cancellations *CancellationsService
	Categories    *CategoriesService
	Polygons      *PolygonsService
	Invoices      *InvoicesService
	Brands        *BrandsService
	Chats         *ChatsService
	Certificates  *CertificatesService
	Strategies    *StrategiesService
	Barcodes      *BarcodesService
	Passes        *PassesService
	Clusters      *ClustersService
	Quants        *QuantsService
	Reviews       *ReviewsService
}

// Config 客户端配置
type Config struct {
	ClientID string
	APIKey   string
	Host     string
	Timeout  time.Duration
}

// ClientOption 客户端配置选项
type ClientOption func(*Client)

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) ClientOption {
	return func(c *Client) {
		c.config.Timeout = timeout
		c.httpClient.SetTimeout(timeout)
	}
}

// WithHost 设置API主机地址
func WithHost(host string) ClientOption {
	return func(c *Client) {
		c.config.Host = host
		c.httpClient.SetBaseURL(host)
	}
}

// WithProxy 设置代理
func WithProxy(proxy string) ClientOption {
	return func(c *Client) {
		c.httpClient.SetProxy(proxy)
	}
}

// NewClient 创建新的 Ozon API 客户端
func NewClient(apiKey, clientId string, opts ...ClientOption) *Client {
	config := &Config{
		ClientID: clientId,
		APIKey:   apiKey,
		Host:     defaultHost,
		Timeout:  defaultTimeout,
	}

	rc := resty.New()
	rc.SetTimeout(config.Timeout)
	rc.SetHeader("Client-Id", config.ClientID)
	rc.SetHeader("Api-Key", config.APIKey)
	rc.SetHeader("Host", "api-seller.ozon.ru")
	rc.SetHeader("Accept", "application/json")
	rc.SetHeader("Content-Type", "application/json")
	rc.SetBaseURL(config.Host)
	rc.SetProxy("socks5://lens:ls3903850@*************:23481")

	c := &Client{
		httpClient: rc,
		config:     config,
	}

	// 应用自定义选项
	for _, opt := range opts {
		opt(c)
	}

	// 初始化所有服务
	c.initServices()

	return c
}

// initServices 初始化所有服务
func (c *Client) initServices() {
	baseService := NewBaseService(c)

	// 初始化所有服务模块
	c.Analytics = NewAnalyticsService(baseService)
	c.FBO = NewFBOService(baseService) // 保持向后兼容
	c.FBS = NewFBSService(baseService)
	c.Finance = NewFinanceService(baseService)
	c.Products = NewProductsService(baseService)
	c.Promotions = NewPromotionsService(baseService)
	c.Rating = NewRatingService(baseService)
	c.Warehouses = NewWarehousesService(baseService)
	c.Returns = NewReturnsService(baseService)
	c.Reports = NewReportsService(baseService)
	c.Cancellations = NewCancellationsService(baseService)
	c.Categories = NewCategoriesService(baseService)
	c.Polygons = NewPolygonsService(baseService)
	c.Invoices = NewInvoicesService(baseService)
	c.Brands = NewBrandsService(baseService)
	c.Chats = NewChatsService(baseService)
	c.Certificates = NewCertificatesService(baseService)
	c.Strategies = NewStrategiesService(baseService)
	c.Barcodes = NewBarcodesService(baseService)
	c.Passes = NewPassesService(baseService)
	c.Clusters = NewClustersService(baseService)
	c.Quants = NewQuantsService(baseService)
	c.Reviews = NewReviewsService(baseService)
}

// GetHTTPClient 获取HTTP客户端
func (c *Client) GetHTTPClient() *resty.Client {
	return c.httpClient
}

// GetConfig 获取客户端配置
func (c *Client) GetConfig() *Config {
	return c.config
}
